/**
 * Comprehensive Logging System
 *
 * Multi-platform logging with async queue processing,
 * debug modes, and performance monitoring.
 */
export interface Logger {
    log(message: string): void;
    error(message: string, error?: Error): void;
    warn(message: string): void;
    info(message: string): void;
    debug(message: string): void;
    performance(label: string, duration: number): void;
}
/**
 * Check if logging is enabled
 */
export declare function isLoggingEnabled(): boolean;
/**
 * Set custom logger (for testing)
 */
export declare function setLogger(logger: Logger): void;
/**
 * Log a message
 */
export declare function log(message: string): void;
/**
 * Log an error
 */
export declare function logError(message: string, error?: Error): void;
/**
 * Log a warning
 */
export declare function logWarn(message: string): void;
/**
 * Log an info message
 */
export declare function logInfo(message: string): void;
/**
 * Log a debug message
 */
export declare function logDebug(message: string): void;
/**
 * Log performance metrics
 */
export declare function logPerformance(label: string, duration: number): void;
/**
 * Performance timer utility
 */
export declare class PerformanceTimer {
    private startTime;
    private label;
    constructor(label: string);
    end(): number;
}
/**
 * Create a performance timer
 */
export declare function createTimer(label: string): PerformanceTimer;
/**
 * Log function execution time
 */
export declare function logExecutionTime<T>(label: string, fn: () => T | Promise<T>): T | Promise<T>;
/**
 * FPS debugging for UI rendering
 */
export declare class FPSDebugger {
    private frameCount;
    private lastTime;
    private fpsHistory;
    frame(): void;
}
/**
 * Global FPS debugger instance
 */
export declare const fpsDebugger: FPSDebugger;
//# sourceMappingURL=log.d.ts.map