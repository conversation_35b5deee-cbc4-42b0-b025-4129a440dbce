#!/usr/bin/env node
/**
 * Simple Kritrima AI CLI - Basic Working Version
 *
 * A simplified version that focuses on core functionality
 * without complex UI components.
 */
import { Command } from 'commander';
import chalk from 'chalk';
import { OpenAI } from 'openai';
// import { CLI_VERSION } from './version';
const CLI_VERSION = '1.0.0';
/**
 * Main CLI application
 */
async function main() {
    const program = new Command();
    program
        .name('kritrima-ai')
        .description('Kritrima AI CLI - Autonomous Coding Assistant')
        .version(CLI_VERSION)
        .option('-m, --model <model>', 'AI model to use', 'gpt-4')
        .option('-p, --provider <provider>', 'AI provider to use', 'openai')
        .option('--api-key <key>', 'API key for the provider')
        .option('--base-url <url>', 'Base URL for the provider')
        .argument('[prompt]', 'Prompt for the AI assistant');
    program.parse();
    const options = program.opts();
    const args = program.args;
    try {
        // Get configuration
        const config = {
            model: options.model,
            provider: options.provider,
            apiKey: options.apiKey || process.env.OPENAI_API_KEY,
            baseURL: options.baseUrl || 'https://api.openai.com/v1',
        };
        // Validate API key
        if (!config.apiKey) {
            console.error(chalk.red('Error: API key is required. Set OPENAI_API_KEY environment variable or use --api-key option.'));
            process.exit(1);
        }
        // Display banner
        console.log(chalk.cyan('╔══════════════════════════════════════════════════════════════╗'));
        console.log(chalk.cyan('║                    Kritrima AI CLI                          ║'));
        console.log(chalk.cyan('║              Autonomous Coding Assistant                    ║'));
        console.log(chalk.cyan('╚══════════════════════════════════════════════════════════════╝'));
        console.log('');
        console.log(chalk.blue(`Model: ${config.model}`));
        console.log(chalk.blue(`Provider: ${config.provider}`));
        console.log('');
        // Handle prompt
        if (args.length > 0) {
            const prompt = args.join(' ');
            await processPrompt(prompt, config);
        }
        else {
            console.log(chalk.yellow('No prompt provided. Use: kritrima-ai "your prompt here"'));
            console.log(chalk.gray('Example: kritrima-ai "Create a simple Node.js HTTP server"'));
        }
    }
    catch (error) {
        console.error(chalk.red('Error:'), error.message);
        process.exit(1);
    }
}
/**
 * Process a single prompt
 */
async function processPrompt(prompt, config) {
    try {
        console.log(chalk.yellow('🤖 Processing your request...'));
        console.log('');
        // Create OpenAI client
        const client = new OpenAI({
            apiKey: config.apiKey,
            baseURL: config.baseURL,
        });
        // Send request to AI
        const response = await client.chat.completions.create({
            model: config.model,
            messages: [
                {
                    role: 'system',
                    content: 'You are an expert software engineer and coding assistant. Provide clear, practical solutions with code examples when appropriate.',
                },
                {
                    role: 'user',
                    content: prompt,
                },
            ],
            max_tokens: 4096,
            temperature: 0.7,
        });
        // Display response
        const content = response.choices[0]?.message?.content;
        if (content) {
            console.log(chalk.green('✅ Response:'));
            console.log('');
            console.log(content);
            console.log('');
        }
        else {
            console.log(chalk.red('❌ No response received from AI'));
        }
        // Display usage info
        if (response.usage) {
            console.log(chalk.gray(`Tokens used: ${response.usage.total_tokens} (prompt: ${response.usage.prompt_tokens}, completion: ${response.usage.completion_tokens})`));
        }
    }
    catch (error) {
        console.error(chalk.red('❌ Failed to process prompt:'), error.message);
        // Provide helpful error messages
        if (error.status === 401) {
            console.error(chalk.yellow('💡 Check your API key is correct and has the necessary permissions.'));
        }
        else if (error.status === 404) {
            console.error(chalk.yellow('💡 Check that the model is available for your provider.'));
        }
        else if (error.code === 'ENOTFOUND') {
            console.error(chalk.yellow('💡 Check your internet connection and base URL.'));
        }
    }
}
/**
 * Display help information
 */
function displayHelp() {
    console.log(chalk.blue('Kritrima AI CLI - Usage Examples:'));
    console.log('');
    console.log(chalk.white('Basic usage:'));
    console.log(chalk.gray('  kritrima-ai "Create a simple Express.js server"'));
    console.log('');
    console.log(chalk.white('With specific model:'));
    console.log(chalk.gray('  kritrima-ai -m gpt-3.5-turbo "Explain async/await in JavaScript"'));
    console.log('');
    console.log(chalk.white('With custom API key:'));
    console.log(chalk.gray('  kritrima-ai --api-key your-key "Write a Python function to sort a list"'));
    console.log('');
    console.log(chalk.white('Environment variables:'));
    console.log(chalk.gray('  OPENAI_API_KEY=your-key kritrima-ai "your prompt"'));
    console.log('');
}
// Handle uncaught errors
process.on('uncaughtException', (error) => {
    console.error(chalk.red('Fatal error:'), error.message);
    process.exit(1);
});
process.on('unhandledRejection', (reason) => {
    console.error(chalk.red('Unhandled promise rejection:'), reason);
    process.exit(1);
});
// Run the CLI
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch((error) => {
        console.error(chalk.red('Failed to start CLI:'), error.message);
        process.exit(1);
    });
}
export { main, processPrompt };
