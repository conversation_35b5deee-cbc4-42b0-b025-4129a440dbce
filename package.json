{"name": "kritrima-ai-cli", "version": "1.0.0", "description": "Comprehensive AI coding assistant with autonomous agent capabilities", "main": "dist/simple-cli.js", "bin": {"kritrima-ai": "./bin/kritrima-ai.js"}, "type": "module", "scripts": {"build": "tsc && npm run copy-assets", "copy-assets": "cp -r src/assets dist/ 2>/dev/null || true", "dev": "tsx src/cli.tsx", "start": "node dist/cli.js", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src/**/*.ts src/**/*.tsx", "lint:fix": "eslint src/**/*.ts src/**/*.tsx --fix", "typecheck": "tsc --noEmit", "clean": "rm -rf dist", "prepare": "npm run build", "prepublishOnly": "npm run clean && npm run build && npm test"}, "keywords": ["ai", "cli", "coding-assistant", "autonomous-agent", "openai", "typescript", "terminal", "development-tools"], "author": "Kritrima AI", "license": "MIT", "engines": {"node": ">=22.0.0"}, "dependencies": {"openai": "^4.67.3", "blessed": "^0.1.81", "commander": "^12.1.0", "yaml": "^2.6.1", "chalk": "^5.3.0", "ora": "^8.1.1", "inquirer": "^12.1.0", "fs-extra": "^11.2.0", "glob": "^11.0.0", "mime-types": "^2.1.35", "sharp": "^0.33.5", "node-notifier": "^10.0.1", "https-proxy-agent": "^7.0.5", "semver": "^7.6.3", "zod": "^3.23.8", "execa": "^9.5.1", "chokidar": "^4.0.1", "dotenv": "^16.4.7", "nanoid": "^5.0.9", "date-fns": "^4.1.0", "lodash": "^4.17.21", "debug": "^4.4.0"}, "devDependencies": {"@types/node": "^22.10.2", "@types/blessed": "^0.1.25", "@types/fs-extra": "^11.0.4", "@types/mime-types": "^2.1.4", "@types/semver": "^7.5.8", "@types/lodash": "^4.17.13", "@types/debug": "^4.1.12", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "eslint": "^9.17.0", "typescript": "^5.8.0", "tsx": "^4.19.2", "vitest": "^2.1.8", "@vitest/coverage-v8": "^2.1.8"}, "repository": {"type": "git", "url": "https://github.com/kritrima/kritrima-ai-cli.git"}, "bugs": {"url": "https://github.com/kritrima/kritrima-ai-cli/issues"}, "homepage": "https://github.com/kritrima/kritrima-ai-cli#readme"}