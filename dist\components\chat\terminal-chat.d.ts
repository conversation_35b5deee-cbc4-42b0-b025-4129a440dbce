/**
 * Terminal Chat Interface
 *
 * Main terminal-based chat interface using blessed.js for rich UI.
 * Handles real-time conversation, model switching, and overlays.
 */
import { EventEmitter } from 'events';
import type { AppConfig } from '../../types/index.js';
export declare class TerminalChat extends EventEmitter {
    private config;
    private screen;
    private chatContainer;
    private inputBox;
    private statusBar;
    private agentLoop;
    private sessionId;
    private items;
    private currentModel;
    private currentProvider;
    private approvalPolicy;
    private overlayMode;
    private loading;
    private terminalSize;
    private currentOverlay;
    private notificationTimeout;
    constructor(config: AppConfig, initialPrompt?: string);
    /**
     * Initialize the blessed screen
     */
    private initializeScreen;
    /**
     * Initialize UI components
     */
    private initializeComponents;
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * Start the terminal chat interface
     */
    start(): Promise<void>;
    /**
     * Handle user input
     */
    private handleInput;
    /**
     * Handle slash commands
     */
    private handleSlashCommand;
    /**
     * Process user input through AI
     */
    private processUserInput;
    /**
     * Add message to chat
     */
    private addMessage;
    /**
     * Add system message
     */
    private addSystemMessage;
    /**
     * Render a message in the chat container
     */
    private renderMessage;
    /**
     * Update status bar
     */
    private updateStatusBar;
    /**
     * Set loading state
     */
    private setLoading;
    /**
     * Scroll to bottom of chat
     */
    private scrollToBottom;
    /**
     * Handle screen resize
     */
    private handleResize;
    /**
     * Start FPS debugging
     */
    private startFPSDebugging;
    /**
     * Process initial prompt
     */
    private processInitialPrompt;
    private showOverlay;
    private closeOverlay;
    private clearConversation;
    private compactConversation;
    private generateBugReport;
    private handleTabCompletion;
    private saveSession;
    /**
     * Shutdown the terminal chat
     */
    shutdown(): void;
}
//# sourceMappingURL=terminal-chat.d.ts.map