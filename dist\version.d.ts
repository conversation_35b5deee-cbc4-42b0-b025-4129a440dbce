/**
 * Version Management for Kritrima AI CLI
 *
 * Provides dynamic version loading from package.json
 */
/**
 * Get the current CLI version from package.json
 */
export declare function getVersion(): string;
/**
 * CLI version constant for easy access
 */
export declare const CLI_VERSION: string;
/**
 * Get version information including build details
 */
export declare function getVersionInfo(): {
    version: string;
    nodeVersion: string;
    platform: string;
    arch: string;
};
/**
 * Format version information for display
 */
export declare function formatVersionInfo(): string;
//# sourceMappingURL=version.d.ts.map