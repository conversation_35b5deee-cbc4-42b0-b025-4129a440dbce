#!/usr/bin/env node

/**
 * Kritrima AI CLI - Entry Point
 * 
 * This is the main entry point for the Kritrima AI CLI application.
 * It handles the initial setup and delegates to the main CLI application.
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Determine if we're running from source or built
const srcPath = join(__dirname, '..', 'src', 'simple-cli.ts');
const distPath = join(__dirname, '..', 'dist', 'simple-cli.js');

let entryPoint;

if (existsSync(distPath)) {
  // Running from built distribution
  entryPoint = distPath;
} else if (existsSync(srcPath)) {
  // Running from source (development)
  entryPoint = srcPath;
} else {
  console.error('Error: Could not find CLI entry point');
  process.exit(1);
}

// Dynamic import to handle both TypeScript and JavaScript
try {
  if (entryPoint.endsWith('.ts')) {
    // Development mode - use tsx to run TypeScript directly
    const { spawn } = await import('child_process');
    const child = spawn('npx', ['tsx', entryPoint, ...process.argv.slice(2)], {
      stdio: 'inherit',
      cwd: join(__dirname, '..')
    });
    
    child.on('exit', (code) => {
      process.exit(code || 0);
    });
  } else {
    // Production mode - run compiled JavaScript
    await import(entryPoint);
  }
} catch (error) {
  console.error('Error starting Kritrima AI CLI:', error.message);
  process.exit(1);
}
